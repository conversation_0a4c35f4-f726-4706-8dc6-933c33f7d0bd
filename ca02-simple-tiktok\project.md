# Tuktak - Social Network MVP

## 1. Introduction

This Business Requirements Document (BRD) outlines the essential features, user stories, and technology considerations for a Minimum Viable Product (MVP) of a social network application similar to TikTok. The primary goal of this MVP is to validate the core concept of short-form video creation, sharing, and consumption within a social context, providing a foundation for future development and expansion.

## 2. Project Goals and Objectives

The main goal of this MVP is to:

*   Launch a functional short-form video social network with core features for content creation, consumption, and basic social interaction.
*   Gather user feedback to iterate and refine the product based on real-world usage.
*   Demonstrate the viability of the concept and attract initial users.

## 3. Scope of the MVP

The MVP will focus on the most critical functionalities required to deliver a compelling user experience for short-form video. Features deemed non-essential for the initial launch will be deferred to future phases.




## 4. Core Features

The following features are identified as core to the MVP, categorized by their priority:

### 4.1. Tier 1: Essential (Must-Have)

These features are fundamental to the application's core functionality and user experience.

*   **User Authentication:** Secure user registration and login mechanisms, including support for email/phone number and potentially social media logins (e.g., Google, Facebook) to ensure ease of access and user management. Password recovery and management features will also be included.

*   **Video Upload & Basic Editing:** This encompasses the ability for users to record new short videos directly within the application or upload existing video files from their device. Basic editing functionalities will include trimming and cutting video segments to allow users to refine their content. A curated library of background music will be available for users to add to their videos, enhancing the creative aspect of content creation.

*   **Personalized Video Feed (For You Page):** The central content consumption experience will be a dynamic, algorithm-driven feed that presents short videos tailored to each user's preferences and engagement patterns. This feed will support seamless vertical scrolling, allowing users to effortlessly discover new content.

*   **User Profile:** Each user will have a dedicated profile page showcasing their uploaded videos. This page will also display essential user information such as their username and profile picture, providing a personal space within the application.

*   **Interaction (Likes & Comments):** Users will be able to express their appreciation for videos by 'liking' them. A robust commenting system will enable users to engage in discussions, share feedback, and interact with content creators and other viewers.

*   **Following/Followers:** The social aspect of the platform will be supported by the ability for users to follow other users whose content they enjoy. This feature will also display follower and following counts on user profiles, indicating their social reach and connections.

### 4.2. Tier 2: Important (Should-Have for Enhanced Experience)

These features are important for a more complete user experience but can be developed after the core functionalities are stable.

*   **Search Functionality:** A search bar will allow users to find specific users by their usernames and discover content by searching for relevant hashtags. This enhances content discoverability and user navigation.

*   **Hashtags:** The implementation of hashtags will enable users to categorize their videos, making them more discoverable through search and trending topics. This also helps in organizing content within the platform.

*   **Basic Analytics (Internal):** For internal purposes, the platform will track key metrics such as video views, likes, and comments. This data will be crucial for understanding content performance, identifying popular trends, and continuously refining the video recommendation algorithm.

### 4.3. Tier 3: Nice-to-Have (Future Considerations)

These features are planned for future iterations beyond the MVP, once the core product has achieved market fit and stability.

*   **Advanced Editing Tools:** This includes a wider range of video editing capabilities such as filters, visual effects, and the ability to add text overlays to videos.
*   **Direct Messaging:** A private messaging system to facilitate one-on-one communication between users.
*   **Live Streaming:** The ability for users to broadcast live video content.
*   **Monetization Features:** Implementation of features that allow content creators to monetize their content, such as virtual gifts or advertising models.
*   **Duet/Stitch functionality:** Features that allow users to create new videos by incorporating parts of existing videos from other creators.




## 5. User Stories

User stories describe the desired functionality from the perspective of different user roles. These stories will guide the development process and ensure that the product meets user needs.

### 5.1. As a New User

*   **Account Registration:** As a new user, I want to register for an account using my email or phone number so that I can access and utilize the platform's features.
*   **Account Login:** As a new user, I want to be able to log in to my existing account securely so that I can continue using the platform where I left off.

### 5.2. As a Content Creator

*   **Video Recording:** As a content creator, I want to record short videos (15-60 seconds) directly within the app so that I can easily capture and share my content.
*   **Video Upload:** As a content creator, I want to upload existing short videos from my device so that I can share pre-recorded content.
*   **Video Trimming/Cutting:** As a content creator, I want to trim or cut sections of my video so that I can refine its length and focus on key moments.
*   **Music Addition:** As a content creator, I want to add background music from a provided library to my video so that it is more engaging and appealing to viewers.
*   **Hashtagging:** As a content creator, I want to add relevant hashtags to my video so that it can be easily discovered by other users interested in similar content.
*   **Profile Management:** As a content creator, I want to have a personal profile page that prominently displays all my uploaded videos so that others can easily browse and view my content.
*   **Follower Tracking:** As a content creator, I want to see how many followers I have so that I can track my audience growth and understand my reach.
*   **Following Tracking:** As a content creator, I want to see how many users I am following so that I can manage my connections and discover new content from them.

### 5.3. As a Content Consumer

*   **Personalized Feed Browsing:** As a content consumer, I want to seamlessly scroll through a personalized video feed (For You Page) so that I can continuously discover new and relevant short video content.
*   **Liking Videos:** As a content consumer, I want to be able to 'like' a video so that I can show my appreciation for the content and provide feedback to the creator.
*   **Commenting on Videos:** As a content consumer, I want to be able to post comments on videos so that I can engage in discussions with the creator and other viewers.
*   **Following Users:** As a content consumer, I want to follow other users whose content I enjoy so that I can see more of their videos in my feed.
*   **User Search:** As a content consumer, I want to search for specific users by their usernames so that I can find their profiles and content directly.
*   **Hashtag Search:** As a content consumer, I want to search for videos using hashtags so that I can find content on specific topics or trends that interest me.

### 5.4. As a Platform Administrator (Internal)

*   **Content Performance Tracking:** As a platform administrator, I want to track key metrics such as video views, likes, and comments so that I can understand content performance, identify popular trends, and continuously refine the video recommendation algorithm to improve user experience.