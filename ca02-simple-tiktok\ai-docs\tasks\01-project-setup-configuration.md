# Task 01: Project Setup & Configuration

## Overview
Set up the foundational project structure for both web (Next.js) and mobile (Flutter) applications with proper tooling, dependencies, and development environment configuration.

## Technical Requirements

### Web Application (Next.js)
- Next.js 14 with App Router
- TypeScript configuration
- Tailwind CSS setup
- ESLint and Prettier configuration
- <PERSON><PERSON><PERSON> for state management
- React Query for server state
- NextAuth.js for authentication
- React Hook Form with Zod validation

### Mobile Application (Flutter)
- Flutter 3.x project setup
- Dart analysis options
- BLoC pattern dependencies
- go_router for navigation
- video_player package
- flutter_secure_storage
- HTTP client setup

## Implementation Steps

### Phase 1: Web Application Setup
1. **Initialize Next.js Project**
   ```bash
   npx create-next-app@latest tiktok-web --typescript --tailwind --eslint --app
   ```

2. **Install Core Dependencies**
   ```bash
   npm install zustand @tanstack/react-query @tanstack/react-query-devtools
   npm install next-auth react-hook-form @hookform/resolvers zod
   npm install @types/node @types/react @types/react-dom
   ```

3. **Install Additional Dependencies**
   ```bash
   npm install lucide-react clsx tailwind-merge
   npm install @headlessui/react @heroicons/react
   ```

4. **Configure TypeScript**
   - Set up strict TypeScript configuration
   - Configure path aliases for clean imports
   - Set up type definitions for API responses

5. **Configure Tailwind CSS**
   - Extend default theme with custom colors
   - Add custom utilities for video player
   - Configure responsive breakpoints

6. **Set up ESLint & Prettier**
   - Configure ESLint rules for Next.js and TypeScript
   - Set up Prettier for code formatting
   - Add pre-commit hooks with husky

### Phase 2: Mobile Application Setup
1. **Initialize Flutter Project**
   ```bash
   flutter create tiktok_mobile
   ```

2. **Configure pubspec.yaml**
   ```yaml
   dependencies:
     flutter_bloc: ^8.1.3
     go_router: ^12.1.1
     video_player: ^2.8.1
     flutter_secure_storage: ^9.0.0
     http: ^1.1.0
     cached_network_image: ^3.3.0
     permission_handler: ^11.0.1
   ```

3. **Set up Project Structure**
   ```
   lib/
   ├── core/
   │   ├── constants/
   │   ├── errors/
   │   ├── network/
   │   └── utils/
   ├── features/
   │   ├── auth/
   │   ├── feed/
   │   ├── profile/
   │   └── upload/
   ├── shared/
   │   ├── widgets/
   │   └── models/
   └── main.dart
   ```

4. **Configure Analysis Options**
   - Set up analysis_options.yaml
   - Configure linting rules
   - Set up import sorting

### Phase 3: Development Environment
1. **Environment Variables**
   - Set up .env files for both platforms
   - Configure API endpoints
   - Set up authentication secrets

2. **Development Scripts**
   - Create package.json scripts for web
   - Set up Flutter development commands
   - Configure build and deployment scripts

3. **Version Control**
   - Set up .gitignore for both platforms
   - Configure Git hooks
   - Set up branch protection rules

## Deliverables
- [ ] Next.js project with TypeScript and Tailwind CSS
- [ ] Flutter project with proper dependencies
- [ ] Development environment configuration
- [ ] ESLint and Prettier setup
- [ ] Environment variables configuration
- [ ] Project documentation (README files)
- [ ] Development scripts and commands

## Acceptance Criteria
- Both projects can be started in development mode
- TypeScript compilation works without errors
- Linting and formatting tools are functional
- Environment variables are properly configured
- Project structure follows best practices
- Documentation is clear and comprehensive

## Dependencies
- None (this is the foundation task)

## Estimated Effort
- Web setup: 4-6 hours
- Mobile setup: 4-6 hours
- Configuration and documentation: 2-3 hours
- **Total: 10-15 hours**

## Notes
- Ensure both platforms use consistent naming conventions
- Set up shared constants for API endpoints
- Consider using a monorepo structure if beneficial
- Document any platform-specific configuration decisions
