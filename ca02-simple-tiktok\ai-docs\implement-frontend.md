# AI Agent Instruction Prompt: Frontend Implementation for TikTok-like MVP

## 1. Overall Goal

Your primary objective is to implement the frontend for a Minimum Viable Product (MVP) of a social network application similar to TikTok. This involves developing both a web application and a mobile application, ensuring a consistent user experience and seamless integration with the backend and video streaming services.

## 2. Technology Stack

*   **Web Frontend:** React/Next.js (Next.js 14 with App Router, React 18, TypeScript, Tailwind CSS, Zustand for global state, React Query for server state, NextAuth.js for authentication, React Hook Form with Zod for forms).
*   **Mobile Frontend:** Flutter (Flutter 3 with Dart, BLoC pattern for state management, go_router for navigation, video_player for video playback, flutter_secure_storage for secure storage).
*   **Backend (for integration context):** TypeScript with Express.js.
*   **Video Streaming Solution (for integration context):** Mux or Cloudinary (choose one based on preference or specific feature needs, but assume one will be used).

## 3. Core Features to Implement (from BRD)

Implement the following core features across both web and mobile platforms, adhering to the detailed plan in `Frontend_Implementation_Plan.md`:

### 3.1. User Authentication
*   User registration (email/password, social login options).
*   User login.
*   Password recovery.
*   Secure session management.

### 3.2. Video Upload & Basic Editing
*   Record/upload short videos (15-60 seconds).
*   Basic trimming/cutting functionality.
*   Add background music from a pre-selected library.
*   Add captions and hashtags.

### 3.3. Personalized Video Feed (For You Page)
*   Algorithm-driven feed displaying short videos.
*   Seamless vertical scrolling.
*   Autoplay with mute/unmute controls.
*   Performance optimizations (lazy loading, prefetching).

### 3.4. User Profile
*   Display user's uploaded videos.
*   Basic profile information (username, profile picture, bio).
*   Profile editing functionality.

### 3.5. Interaction (Likes & Comments)
*   Ability to 'like' videos (optimistic updates).
*   Ability to post and view comments (threaded discussions).

### 3.6. Following/Followers
*   Ability to follow/unfollow other users.
*   Display follower/following counts.

### 3.7. Search Functionality
*   Search for users and hashtags.
*   Autocomplete and categorized results.

### 3.8. Hashtags
*   Support for adding and displaying hashtags.

## 4. Detailed Implementation Guidelines

Refer to the `Frontend_Implementation_Plan.md` file for detailed architectural outlines, component breakdowns, state management strategies, performance optimization techniques, and responsive design considerations for both web and mobile platforms. **You MUST strictly follow the guidelines and structures outlined in that document.**

## 5. Integration Points

### 5.1. Backend API (TypeScript/Express.js)
*   All data fetching and mutations will be done via RESTful API calls to the backend.
*   Implement proper authentication (e.g., JWT tokens) for all API requests.
*   Handle API errors gracefully and provide user feedback.

### 5.2. Video Streaming Solution (Mux or Cloudinary)
*   **Video Upload:** Implement direct video uploads from the client to the chosen video streaming service using signed URLs obtained from the backend. If direct upload is not feasible for a specific platform/scenario, implement backend-proxied uploads.
*   **Video Playback:** Consume optimized video playback URLs (HLS/DASH) provided by the backend, which originate from the video streaming service's CDN.
*   **Thumbnails/Previews:** Utilize thumbnail and preview URLs provided by the video streaming service.

## 6. Deliverables

Upon completion, provide the following:

*   The complete source code for both the Web (React/Next.js) and Mobile (Flutter) frontend applications.
*   Clear instructions on how to set up, build, and run both applications locally.
*   Any necessary documentation regarding specific implementation choices or challenges encountered.

## 7. Constraints and Assumptions

*   Assume the TypeScript/Express.js backend API is available and correctly implemented according to the defined endpoints.
*   Assume the chosen video streaming service (Mux or Cloudinary) is configured and accessible.
*   Focus on functionality and user experience for the MVP; advanced UI/UX polish can be deferred.
*   Prioritize cross-platform consistency where feasible, but leverage platform-specific optimizations when necessary for performance.

Good luck!


