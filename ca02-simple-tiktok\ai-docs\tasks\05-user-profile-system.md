# Task 05: User Profile System

## Overview
Implement comprehensive user profile functionality including profile display, editing capabilities, user video galleries, and profile customization for both web and mobile platforms.

## Technical Requirements

### Web Application Features
- User profile page with responsive design
- Profile editing form with validation
- User video grid display
- Profile picture upload and cropping
- Bio editing with character limits
- Social stats display (followers, following, likes)
- Profile sharing functionality

### Mobile Application Features
- Native profile screen with smooth animations
- Image picker for profile picture
- In-app profile editing
- Video grid with lazy loading
- Pull-to-refresh functionality
- Profile caching for offline viewing

## Implementation Steps

### Phase 1: Web Profile Display
1. **Profile Page Component**
   ```typescript
   // app/profile/[username]/page.tsx
   interface ProfilePageProps {
     params: { username: string };
   }
   
   // components/profile/ProfilePage.tsx
   interface ProfileData {
     id: string;
     username: string;
     displayName: string;
     bio: string;
     profilePicture: string;
     followerCount: number;
     followingCount: number;
     videoCount: number;
     isFollowing: boolean;
   }
   ```

2. **Profile Header Component**
   ```typescript
   // components/profile/ProfileHeader.tsx
   - Profile picture with fallback
   - Username and display name
   - Bio text with hashtag/mention parsing
   - Follow/unfollow button
   - Social stats (followers, following, videos)
   - Edit profile button (own profile)
   ```

3. **Video Grid Component**
   ```typescript
   // components/profile/VideoGrid.tsx
   interface VideoGridProps {
     videos: Video[];
     onVideoSelect: (video: Video) => void;
     isLoading: boolean;
   }
   ```

4. **Profile Navigation Tabs**
   ```typescript
   // components/profile/ProfileTabs.tsx
   - Videos tab (default)
   - Liked videos tab (private)
   - Saved videos tab (private)
   ```

### Phase 2: Mobile Profile Screen
1. **Profile Screen Widget**
   ```dart
   // features/profile/screens/profile_screen.dart
   class ProfileScreen extends StatefulWidget {
     final String userId;
     
     @override
     _ProfileScreenState createState() => _ProfileScreenState();
   }
   ```

2. **Profile BLoC**
   ```dart
   // features/profile/bloc/profile_bloc.dart
   class ProfileBloc extends Bloc<ProfileEvent, ProfileState> {
     on<ProfileRequested>(_onProfileRequested);
     on<ProfileUpdated>(_onProfileUpdated);
     on<FollowToggled>(_onFollowToggled);
   }
   ```

3. **Profile Header Widget**
   ```dart
   // features/profile/widgets/profile_header.dart
   class ProfileHeader extends StatelessWidget {
     final UserProfile profile;
     final bool isOwnProfile;
   }
   ```

4. **Video Grid Widget**
   ```dart
   // features/profile/widgets/video_grid_widget.dart
   class VideoGridWidget extends StatelessWidget {
     final List<Video> videos;
     final VoidCallback? onLoadMore;
   }
   ```

### Phase 3: Profile Editing
1. **Edit Profile Form (Web)**
   ```typescript
   // components/profile/EditProfileForm.tsx
   interface EditProfileData {
     displayName: string;
     bio: string;
     profilePicture?: File;
     website?: string;
   }
   
   const editProfileSchema = z.object({
     displayName: z.string().min(1).max(50),
     bio: z.string().max(150),
     website: z.string().url().optional()
   });
   ```

2. **Profile Picture Upload**
   ```typescript
   // components/profile/ProfilePictureUpload.tsx
   - Image selection and preview
   - Crop functionality
   - Upload progress
   - Error handling
   ```

3. **Edit Profile Screen (Mobile)**
   ```dart
   // features/profile/screens/edit_profile_screen.dart
   class EditProfileScreen extends StatefulWidget {
     final UserProfile currentProfile;
   }
   ```

4. **Image Picker Service**
   ```dart
   // core/services/image_picker_service.dart
   class ImagePickerService {
     Future<XFile?> pickImageFromGallery();
     Future<XFile?> takePhoto();
     Future<Uint8List> cropImage(XFile image);
   }
   ```

### Phase 4: Profile Data Management
1. **Profile Repository**
   ```typescript
   // repositories/profile-repository.ts
   class ProfileRepository {
     async getProfile(username: string): Promise<UserProfile>
     async updateProfile(data: EditProfileData): Promise<UserProfile>
     async uploadProfilePicture(file: File): Promise<string>
   }
   ```

2. **Profile Store (Web)**
   ```typescript
   // stores/profile-store.ts
   interface ProfileState {
     currentProfile: UserProfile | null;
     viewedProfiles: Map<string, UserProfile>;
     isLoading: boolean;
     error: string | null;
   }
   ```

3. **Profile Cache (Mobile)**
   ```dart
   // core/services/profile_cache_service.dart
   class ProfileCacheService {
     Future<void> cacheProfile(UserProfile profile);
     Future<UserProfile?> getCachedProfile(String userId);
     Future<void> clearCache();
   }
   ```

### Phase 5: Social Features Integration
1. **Follow/Unfollow Functionality**
   ```typescript
   // services/social-service.ts
   class SocialService {
     async followUser(userId: string): Promise<void>
     async unfollowUser(userId: string): Promise<void>
     async getFollowers(userId: string): Promise<User[]>
     async getFollowing(userId: string): Promise<User[]>
   }
   ```

2. **Social Stats Component**
   ```typescript
   // components/profile/SocialStats.tsx
   - Follower count with formatting
   - Following count
   - Total likes received
   - Click handlers for follower/following lists
   ```

3. **Follow Button Component**
   ```typescript
   // components/profile/FollowButton.tsx
   - Follow/unfollow state management
   - Optimistic updates
   - Loading states
   - Error handling
   ```

### Phase 6: Profile Customization
1. **Theme Selection**
   ```typescript
   // components/profile/ProfileTheme.tsx
   - Color scheme options
   - Profile layout variants
   - Preview functionality
   ```

2. **Privacy Settings**
   ```typescript
   // components/profile/PrivacySettings.tsx
   - Private/public profile toggle
   - Video visibility settings
   - Blocked users management
   ```

3. **Profile Verification**
   ```typescript
   // components/profile/VerificationBadge.tsx
   - Verification status display
   - Verification request flow
   ```

## Deliverables
- [ ] User profile display page/screen
- [ ] Profile editing functionality
- [ ] Profile picture upload and cropping
- [ ] User video grid with pagination
- [ ] Follow/unfollow functionality
- [ ] Social stats display
- [ ] Profile caching system
- [ ] Privacy settings
- [ ] Profile sharing features
- [ ] Responsive design implementation

## Acceptance Criteria
- Profile pages load quickly and display correctly
- Users can edit their profile information
- Profile pictures can be uploaded and cropped
- Video grids load efficiently with pagination
- Follow/unfollow works with optimistic updates
- Social stats are accurate and real-time
- Profile data is cached appropriately
- Privacy settings are respected
- Profile sharing generates correct links
- Works seamlessly on all screen sizes

## Dependencies
- Task 01: Project Setup & Configuration
- Task 02: Authentication System
- Task 03: Video Upload & Editing (for video data)
- Backend API for profile endpoints
- Image storage service integration

## Estimated Effort
- Web profile display: 8-10 hours
- Mobile profile screen: 6-8 hours
- Profile editing: 10-12 hours
- Image upload/cropping: 8-10 hours
- Social features: 6-8 hours
- Data management: 6-8 hours
- Customization features: 4-6 hours
- Testing and optimization: 6-8 hours
- **Total: 54-70 hours**

## Notes
- Implement proper image optimization for profile pictures
- Consider implementing profile analytics for content creators
- Add accessibility features for profile navigation
- Implement proper error boundaries for profile loading
- Consider implementing profile templates
- Add support for profile verification badges
- Implement profile backup and export functionality
