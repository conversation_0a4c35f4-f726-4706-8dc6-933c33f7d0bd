# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Next.js
tiktok-web/.next/
tiktok-web/out/
tiktok-web/build/
tiktok-web/dist/

# Production builds
build/
dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
*.log
logs/

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Flutter/Dart specific
tiktok_mobile/.dart_tool/
tiktok_mobile/.flutter-plugins
tiktok_mobile/.flutter-plugins-dependencies
tiktok_mobile/.packages
tiktok_mobile/.pub-cache/
tiktok_mobile/.pub/
tiktok_mobile/build/
tiktok_mobile/ios/Flutter/Generated.xcconfig
tiktok_mobile/ios/Runner/GeneratedPluginRegistrant.*
tiktok_mobile/android/app/src/main/java/io/flutter/plugins/GeneratedPluginRegistrant.java
tiktok_mobile/android/local.properties
tiktok_mobile/android/key.properties

# Database
*.db
*.sqlite
*.sqlite3

# Uploads
uploads/
public/uploads/

# Cache
.cache/

# Vercel
.vercel

# TypeScript
*.tsbuildinfo

# Testing
coverage/
.nyc_output/

# Misc
*.tgz
*.tar.gz
