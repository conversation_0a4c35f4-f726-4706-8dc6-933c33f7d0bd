# Tuktak Web Application

The web frontend for the Tuktak social network, built with Next.js 14, TypeScript, and Tailwind CSS.

## 🛠️ Technology Stack

- **Framework**: Next.js 14 with App Router and Turbopack
- **Language**: TypeScript with strict configuration
- **Styling**: Tailwind CSS v4 with custom theme
- **State Management**: Zustand for client state
- **Server State**: TanStack Query (React Query)
- **Authentication**: NextAuth.js
- **Forms**: React Hook Form with Zod validation
- **UI Components**: Headless UI, Heroicons, Lucide React
- **Code Quality**: ESLint, Prettier with Tailwind plugin

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ and npm
- Environment variables configured

### Installation

1. **Install dependencies**

   ```bash
   npm install
   ```

2. **Set up environment variables**

   ```bash
   cp .env.example .env.local
   # Edit .env.local with your configuration
   ```

3. **Start development server**

   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📝 Available Scripts

- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues
- `npm run format` - Format code with Prettier
- `npm run format:check` - Check code formatting
- `npm run type-check` - Run TypeScript type checking

## 🎨 Styling & Theming

This project uses Tailwind CSS v4 with a custom theme configuration in `app/globals.css`:

### Custom Colors

- **Primary**: `#fe2c55` (TikTok red)
- **Secondary**: `#25f4ee` (TikTok cyan)
- **Accent**: `#ff6b35`
- **Dark**: `#161823` (TikTok dark)

### Custom Utilities

- Video player components
- Feed scroll behavior
- Animation utilities
- Responsive breakpoints

## 🏗️ Project Structure

```
tiktok-web/
├── app/                 # Next.js App Router
│   ├── globals.css     # Global styles and Tailwind config
│   ├── layout.tsx      # Root layout
│   └── page.tsx        # Home page
├── components/         # Reusable components (to be created)
├── lib/               # Utility functions (to be created)
├── hooks/             # Custom React hooks (to be created)
├── stores/            # Zustand stores (to be created)
├── types/             # TypeScript type definitions (to be created)
└── public/            # Static assets
```

## 🔧 Configuration

### Environment Variables

Required environment variables (see `.env.example`):

- `NEXTAUTH_URL` - Application URL
- `NEXTAUTH_SECRET` - Authentication secret
- `DATABASE_URL` - Database connection string
- `API_BASE_URL` - Backend API URL

### TypeScript

- Strict mode enabled
- Path aliases configured (`@/*`)
- Type checking on build

### ESLint & Prettier

- Next.js recommended rules
- TypeScript support
- Tailwind CSS class sorting
- Automatic formatting on save (when configured)

## 🚀 Deployment

### Vercel (Recommended)

1. Connect your repository to Vercel
2. Configure environment variables
3. Deploy automatically on push

### Manual Deployment

```bash
npm run build
npm run start
```

## 🧪 Testing

Testing setup will be added in future tasks. Recommended stack:

- **Unit Tests**: Jest + React Testing Library
- **E2E Tests**: Playwright
- **Component Tests**: Storybook

## 📚 Learn More

- [Next.js Documentation](https://nextjs.org/docs)
- [Tailwind CSS v4](https://tailwindcss.com/docs)
- [TypeScript](https://www.typescriptlang.org/docs)
- [Zustand](https://zustand-demo.pmnd.rs)
- [TanStack Query](https://tanstack.com/query/latest)
