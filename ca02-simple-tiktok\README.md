# Tuktak - Social Network MVP

A TikTok-like social network application built with Next.js (web) and Flutter (mobile). This MVP focuses on short-form video creation, sharing, and consumption within a social context.

## 🚀 Features

### Tier 1: Essential (Must-Have)
- **User Authentication**: Secure registration and login with social media support
- **Video Upload & Basic Editing**: Record/upload videos with trimming and music
- **Personalized Video Feed**: Algorithm-driven "For You" page with vertical scrolling
- **User Profile**: Personal profile pages with uploaded videos
- **Interaction**: Like and comment on videos
- **Following/Followers**: Social connections and follower counts

### Tier 2: Important (Should-Have)
- **Search Functionality**: Find users and content by hashtags
- **Hashtags**: Content categorization and discovery
- **Basic Analytics**: Internal metrics tracking

### Tier 3: Nice-to-Have (Future)
- Advanced editing tools, direct messaging, live streaming, monetization

## 🏗️ Project Structure

```
tuktak-social-network/
├── tiktok-web/          # Next.js web application
├── tiktok_mobile/       # Flutter mobile application (requires Flutter SDK)
├── ai-docs/             # Project documentation and tasks
├── package.json         # Root package.json with workspace scripts
└── README.md           # This file
```

## 🛠️ Technology Stack

### Web Application (Next.js)
- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS v4
- **State Management**: Zustand
- **Server State**: TanStack Query (React Query)
- **Authentication**: NextAuth.js
- **Forms**: React Hook Form with Zod validation
- **UI Components**: Headless UI, Heroicons, Lucide React

### Mobile Application (Flutter)
- **Framework**: Flutter 3.x
- **State Management**: BLoC pattern
- **Navigation**: go_router
- **Video**: video_player package
- **Storage**: flutter_secure_storage
- **HTTP**: http package

## 🚦 Getting Started

### Prerequisites
- Node.js 18+ and npm
- Flutter SDK 3.x (for mobile development)
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd tuktak-social-network
   ```

2. **Install dependencies**
   ```bash
   npm run install:all
   ```

3. **Set up environment variables**
   ```bash
   cp tiktok-web/.env.example tiktok-web/.env.local
   # Edit .env.local with your configuration
   ```

4. **Start development servers**
   ```bash
   # Web application
   npm run dev:web
   
   # Mobile application (requires Flutter)
   npm run dev:mobile
   ```

## 📝 Available Scripts

### Root Level Scripts
- `npm run dev` - Start web development server
- `npm run build` - Build web application
- `npm run start` - Start production web server
- `npm run lint` - Lint all projects
- `npm run format` - Format all projects
- `npm run test` - Run all tests
- `npm run clean` - Clean all build artifacts
- `npm run setup` - Install dependencies and build

### Platform-Specific Scripts
- `npm run dev:web` - Start web development server
- `npm run dev:mobile` - Start mobile development
- `npm run build:web` - Build web application
- `npm run build:mobile` - Build mobile APK
- `npm run lint:web` - Lint web application
- `npm run lint:mobile` - Lint mobile application

## 🔧 Configuration

### Environment Variables
Copy `.env.example` to `.env.local` and configure:
- Database connection
- Authentication secrets
- OAuth provider credentials
- File storage configuration
- API endpoints

### Development Tools
- **ESLint**: Code linting with Next.js rules
- **Prettier**: Code formatting with Tailwind CSS plugin
- **TypeScript**: Strict type checking
- **Husky**: Git hooks (when configured)

## 📱 Mobile Development

**Note**: Flutter SDK is required for mobile development. Install Flutter from [flutter.dev](https://flutter.dev) and ensure it's in your PATH.

The mobile application structure follows clean architecture principles:
```
lib/
├── core/           # Core utilities and constants
├── features/       # Feature-based modules
├── shared/         # Shared widgets and models
└── main.dart      # Application entry point
```

## 🚀 Deployment

### Web Application
- **Vercel**: Recommended for Next.js applications
- **Netlify**: Alternative deployment platform
- **Docker**: Containerized deployment

### Mobile Application
- **Google Play Store**: Android deployment
- **Apple App Store**: iOS deployment (requires macOS)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🔗 Links

- [Project Documentation](./ai-docs/)
- [Business Requirements](./ai-docs/Business_Requirements_Document.md)
- [Task Breakdown](./ai-docs/tasks/)
