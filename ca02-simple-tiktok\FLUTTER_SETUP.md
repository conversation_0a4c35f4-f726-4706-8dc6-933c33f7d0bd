# Flutter Mobile Application Setup

This document provides instructions for setting up the Flutter mobile application when Flutter SDK is available.

## Prerequisites

1. **Install Flutter SDK**
   - Download from [flutter.dev](https://flutter.dev/docs/get-started/install)
   - Add Flutter to your PATH
   - Run `flutter doctor` to verify installation

2. **Platform-specific requirements**
   - **Android**: Android Studio, Android SDK
   - **iOS**: Xcode (macOS only), iOS SDK

## Setup Instructions

### 1. Create Flutter Project

```bash
cd ca02-simple-tiktok
flutter create tiktok_mobile
cd tiktok_mobile
```

### 2. Configure pubspec.yaml

Replace the dependencies section in `pubspec.yaml`:

```yaml
dependencies:
  flutter:
    sdk: flutter
  
  # State Management
  flutter_bloc: ^8.1.3
  
  # Navigation
  go_router: ^12.1.1
  
  # Video
  video_player: ^2.8.1
  
  # Storage
  flutter_secure_storage: ^9.0.0
  
  # Network
  http: ^1.1.0
  
  # UI
  cached_network_image: ^3.3.0
  
  # Permissions
  permission_handler: ^11.0.1
  
  # Utilities
  equatable: ^2.0.5
  json_annotation: ^4.8.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
```

### 3. Project Structure

Create the following directory structure:

```
lib/
├── core/
│   ├── constants/
│   │   ├── api_constants.dart
│   │   ├── app_constants.dart
│   │   └── route_constants.dart
│   ├── errors/
│   │   ├── exceptions.dart
│   │   └── failures.dart
│   ├── network/
│   │   ├── api_client.dart
│   │   └── network_info.dart
│   └── utils/
│       ├── validators.dart
│       └── helpers.dart
├── features/
│   ├── auth/
│   │   ├── data/
│   │   ├── domain/
│   │   └── presentation/
│   ├── feed/
│   │   ├── data/
│   │   ├── domain/
│   │   └── presentation/
│   ├── profile/
│   │   ├── data/
│   │   ├── domain/
│   │   └── presentation/
│   └── upload/
│       ├── data/
│       ├── domain/
│       └── presentation/
├── shared/
│   ├── widgets/
│   │   ├── custom_button.dart
│   │   ├── custom_text_field.dart
│   │   └── video_player_widget.dart
│   └── models/
│       ├── user_model.dart
│       └── video_model.dart
└── main.dart
```

### 4. Configure Analysis Options

Create `analysis_options.yaml`:

```yaml
include: package:flutter_lints/flutter.yaml

analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
  
  strong-mode:
    implicit-casts: false
    implicit-dynamic: false

linter:
  rules:
    - prefer_single_quotes
    - sort_constructors_first
    - sort_unnamed_constructors_first
    - always_declare_return_types
    - avoid_print
    - avoid_unnecessary_containers
    - prefer_const_constructors
    - prefer_const_literals_to_create_immutables
    - sized_box_for_whitespace
    - use_key_in_widget_constructors
```

### 5. Main Application Setup

Update `lib/main.dart`:

```dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

void main() {
  runApp(const TiktokMobileApp());
}

class TiktokMobileApp extends StatelessWidget {
  const TiktokMobileApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: 'Tuktak',
      theme: ThemeData(
        primarySwatch: Colors.red,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      routerConfig: _router,
    );
  }
}

final GoRouter _router = GoRouter(
  routes: [
    GoRoute(
      path: '/',
      builder: (context, state) => const FeedScreen(),
    ),
    GoRoute(
      path: '/auth',
      builder: (context, state) => const AuthScreen(),
    ),
    GoRoute(
      path: '/profile',
      builder: (context, state) => const ProfileScreen(),
    ),
    GoRoute(
      path: '/upload',
      builder: (context, state) => const UploadScreen(),
    ),
  ],
);

// Placeholder screens
class FeedScreen extends StatelessWidget {
  const FeedScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(
    body: Center(child: Text('Feed Screen')),
  );
}

class AuthScreen extends StatelessWidget {
  const AuthScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(
    body: Center(child: Text('Auth Screen')),
  );
}

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(
    body: Center(child: Text('Profile Screen')),
  );
}

class UploadScreen extends StatelessWidget {
  const UploadScreen({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(
    body: Center(child: Text('Upload Screen')),
  );
}
```

### 6. Install Dependencies

```bash
flutter pub get
```

### 7. Run the Application

```bash
# For Android
flutter run

# For iOS (macOS only)
flutter run -d ios

# For web
flutter run -d web
```

## Development Commands

```bash
# Get dependencies
flutter pub get

# Run app
flutter run

# Build APK
flutter build apk

# Build iOS (macOS only)
flutter build ios

# Run tests
flutter test

# Analyze code
flutter analyze

# Format code
dart format .

# Clean build
flutter clean
```

## Platform Configuration

### Android
- Minimum SDK: 21 (Android 5.0)
- Target SDK: 34 (Android 14)
- Permissions: Camera, Microphone, Storage

### iOS
- Minimum iOS: 12.0
- Permissions: Camera, Microphone, Photo Library

## Next Steps

1. Implement authentication feature
2. Set up video player functionality
3. Create feed UI components
4. Implement upload functionality
5. Add state management with BLoC
6. Configure API integration
7. Add testing setup

## Resources

- [Flutter Documentation](https://flutter.dev/docs)
- [BLoC Pattern](https://bloclibrary.dev)
- [Go Router](https://pub.dev/packages/go_router)
- [Video Player](https://pub.dev/packages/video_player)
