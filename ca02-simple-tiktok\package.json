{"name": "tuktak-social-network", "version": "1.0.0", "description": "A TikTok-like social network MVP with web and mobile applications", "private": true, "workspaces": ["tiktok-web", "tiktok_mobile"], "scripts": {"dev": "npm run dev --workspace=tiktok-web", "dev:web": "npm run dev --workspace=tiktok-web", "dev:mobile": "cd tiktok_mobile && flutter run", "build": "npm run build --workspace=tiktok-web", "build:web": "npm run build --workspace=tiktok-web", "build:mobile": "cd tiktok_mobile && flutter build apk", "start": "npm run start --workspace=tiktok-web", "start:web": "npm run start --workspace=tiktok-web", "lint": "npm run lint --workspace=tiktok-web", "lint:web": "npm run lint --workspace=tiktok-web", "lint:mobile": "cd tiktok_mobile && dart analyze", "format": "npm run format --workspace=tiktok-web", "format:web": "npm run format --workspace=tiktok-web", "format:mobile": "cd tiktok_mobile && dart format .", "test": "npm run test --workspace=tiktok-web", "test:web": "npm run test --workspace=tiktok-web", "test:mobile": "cd tiktok_mobile && flutter test", "clean": "npm run clean:web && npm run clean:mobile", "clean:web": "cd tiktok-web && rm -rf .next node_modules", "clean:mobile": "cd tiktok_mobile && flutter clean", "install:all": "npm install && npm install --workspace=tiktok-web", "setup": "npm run install:all && npm run build:web"}, "keywords": ["social-network", "video-sharing", "tiktok", "nextjs", "flutter", "mvp"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}