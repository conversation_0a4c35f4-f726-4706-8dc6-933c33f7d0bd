# Task 03: Video Upload & Basic Editing

## Overview
Implement video recording, upload, and basic editing functionality for both web and mobile platforms, including trimming, background music, captions, and hashtags.

## Technical Requirements

### Web Application Features
- Video recording using MediaRecorder API
- File upload with progress tracking
- Basic video trimming interface
- Background music selection and overlay
- Caption and hashtag input
- Video preview before publishing
- Integration with video streaming service (Mux/Cloudinary)

### Mobile Application Features
- Camera integration for video recording
- Gallery video selection
- Native video trimming capabilities
- Audio overlay functionality
- Text overlay for captions
- Hashtag parsing and validation
- Upload progress indication

## Implementation Steps

### Phase 1: Web Video Recording & Upload
1. **Video Recording Component**
   ```typescript
   // components/video/VideoRecorder.tsx
   interface VideoRecorderProps {
     onRecordingComplete: (blob: Blob) => void;
     maxDuration: number; // 15-60 seconds
   }
   ```

2. **File Upload Service**
   ```typescript
   // services/upload-service.ts
   class VideoUploadService {
     async uploadVideo(file: File): Promise<UploadResponse>
     async getSignedUploadUrl(): Promise<SignedUrlResponse>
   }
   ```

3. **Video Upload Component**
   ```typescript
   // components/video/VideoUpload.tsx
   - Drag & drop interface
   - File validation (format, size, duration)
   - Upload progress bar
   - Error handling
   ```

4. **Video Trimming Interface**
   ```typescript
   // components/video/VideoTrimmer.tsx
   - Timeline scrubber
   - Start/end time selectors
   - Preview playback
   - Trim confirmation
   ```

### Phase 2: Mobile Video Capture & Upload
1. **Camera Service**
   ```dart
   // core/services/camera_service.dart
   class CameraService {
     Future<XFile?> recordVideo({required int maxDuration});
     Future<XFile?> pickVideoFromGallery();
   }
   ```

2. **Video Upload BLoC**
   ```dart
   // features/upload/bloc/upload_bloc.dart
   class UploadBloc extends Bloc<UploadEvent, UploadState> {
     on<VideoSelected>(_onVideoSelected);
     on<UploadStarted>(_onUploadStarted);
     on<UploadProgress>(_onUploadProgress);
   }
   ```

3. **Video Recording Screen**
   ```dart
   // features/upload/screens/video_recording_screen.dart
   - Camera preview
   - Recording controls
   - Duration indicator
   - Flash toggle
   - Camera flip
   ```

4. **Video Editing Screen**
   ```dart
   // features/upload/screens/video_editing_screen.dart
   - Video preview
   - Trimming controls
   - Music selection
   - Caption input
   ```

### Phase 3: Basic Video Editing Features
1. **Video Trimming Logic**
   ```typescript
   // Web: Using FFmpeg.wasm or similar
   // Mobile: Using native video editing APIs
   
   interface TrimOptions {
     startTime: number;
     endTime: number;
     outputFormat: 'mp4' | 'webm';
   }
   ```

2. **Background Music Integration**
   ```typescript
   // components/video/MusicSelector.tsx
   - Music library browser
   - Audio preview
   - Volume control
   - Music trimming to match video length
   ```

3. **Caption and Hashtag Input**
   ```typescript
   // components/video/CaptionEditor.tsx
   interface CaptionData {
     text: string;
     hashtags: string[];
     mentions: string[];
   }
   ```

### Phase 4: Video Processing & Upload
1. **Video Compression**
   ```typescript
   // utils/video-compression.ts
   - Optimize video for web/mobile
   - Multiple quality options
   - Maintain aspect ratio
   ```

2. **Upload Progress Tracking**
   ```typescript
   // stores/upload-store.ts
   interface UploadState {
     progress: number;
     status: 'idle' | 'uploading' | 'processing' | 'complete' | 'error';
     error?: string;
   }
   ```

3. **Video Streaming Service Integration**
   ```typescript
   // services/video-streaming.ts
   // For Mux integration
   class MuxVideoService {
     async createUploadUrl(): Promise<string>
     async getVideoStatus(uploadId: string): Promise<VideoStatus>
   }
   ```

### Phase 5: Upload Flow UI/UX
1. **Upload Progress Screen**
   - Visual progress indicator
   - Cancel upload option
   - Background upload support
   - Error recovery

2. **Video Preview & Publish**
   ```typescript
   // components/video/VideoPublisher.tsx
   - Final video preview
   - Caption editing
   - Privacy settings
   - Publish button
   ```

3. **Upload Queue Management**
   - Multiple video uploads
   - Retry failed uploads
   - Upload scheduling

## Deliverables
- [ ] Web video recording component
- [ ] Mobile camera integration
- [ ] Video file upload with progress tracking
- [ ] Basic video trimming functionality
- [ ] Background music selection and overlay
- [ ] Caption and hashtag input system
- [ ] Video compression and optimization
- [ ] Upload queue management
- [ ] Error handling and recovery
- [ ] Video streaming service integration

## Acceptance Criteria
- Users can record videos up to 60 seconds
- Users can upload videos from device storage
- Basic trimming works accurately
- Background music can be added and adjusted
- Captions and hashtags are properly parsed
- Upload progress is clearly indicated
- Failed uploads can be retried
- Videos are properly compressed for streaming
- Integration with video service works seamlessly
- UI is intuitive and responsive

## Dependencies
- Task 01: Project Setup & Configuration
- Task 02: Authentication System
- Backend API for video upload endpoints
- Video streaming service (Mux/Cloudinary) setup

## Estimated Effort
- Web video recording: 8-10 hours
- Mobile camera integration: 6-8 hours
- Video trimming functionality: 10-12 hours
- Music and caption features: 8-10 hours
- Upload system: 8-10 hours
- Video processing: 6-8 hours
- UI/UX implementation: 8-10 hours
- Testing and optimization: 6-8 hours
- **Total: 60-76 hours**

## Notes
- Consider using WebCodecs API for web video processing
- Implement proper video format validation
- Handle device permissions gracefully
- Optimize for different device capabilities
- Consider offline upload queue for mobile
- Implement proper video metadata extraction
- Test on various devices and browsers
