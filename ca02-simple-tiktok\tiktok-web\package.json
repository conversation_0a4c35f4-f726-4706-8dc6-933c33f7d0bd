{"name": "tiktok-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.2.1", "@tanstack/react-query": "^5.84.0", "@tanstack/react-query-devtools": "^5.84.0", "clsx": "^2.1.1", "lucide-react": "^0.535.0", "next": "15.4.5", "next-auth": "^4.24.11", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.61.1", "tailwind-merge": "^3.3.1", "zod": "^4.0.14", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^4", "typescript": "^5"}}