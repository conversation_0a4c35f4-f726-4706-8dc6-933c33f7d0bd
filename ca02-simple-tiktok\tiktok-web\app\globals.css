@import 'tailwindcss';

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* Custom colors for TikTok-like app */
  --color-primary: #fe2c55;
  --color-primary-dark: #e91e63;
  --color-secondary: #25f4ee;
  --color-accent: #ff6b35;
  --color-dark: #161823;
  --color-dark-light: #1e1e2e;
  --color-gray-light: #f1f1f2;
  --color-gray-medium: #a8a8a8;
  --color-gray-dark: #6b7280;

  /* Video player specific colors */
  --color-video-overlay: rgba(0, 0, 0, 0.3);
  --color-video-controls: rgba(255, 255, 255, 0.9);
  --color-video-progress: #fe2c55;

  /* Responsive breakpoints */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Custom utilities for video player */
.video-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.video-player {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--color-video-overlay);
  pointer-events: none;
}

.video-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 1rem;
  pointer-events: auto;
}

.video-progress {
  height: 2px;
  background: var(--color-video-progress);
  transition: all 0.3s ease;
}

/* Feed scroll utilities */
.feed-container {
  height: 100vh;
  overflow-y: auto;
  scroll-snap-type: y mandatory;
}

.feed-item {
  height: 100vh;
  scroll-snap-align: start;
  scroll-snap-stop: always;
}

/* Animation utilities */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
