# Task 06: Interaction Features (Likes & Comments)

## Overview
Implement comprehensive video interaction features including likes, comments, threaded discussions, and real-time updates for both web and mobile platforms with optimistic UI updates.

## Technical Requirements

### Web Application Features
- Like/unlike functionality with animations
- Comment system with threaded replies
- Real-time comment updates
- Comment moderation features
- Emoji reactions and mentions
- Comment sorting and filtering
- Optimistic UI updates

### Mobile Application Features
- Native gesture-based interactions
- Smooth like animations
- Comment bottom sheet interface
- Push notifications for interactions
- Offline interaction queuing
- Haptic feedback for interactions

## Implementation Steps

### Phase 1: Like System Implementation
1. **Like Button Component (Web)**
   ```typescript
   // components/interactions/LikeButton.tsx
   interface LikeButtonProps {
     videoId: string;
     initialLikeCount: number;
     isLiked: boolean;
     onLike: (videoId: string) => void;
     onUnlike: (videoId: string) => void;
   }
   ```

2. **Like Animation**
   ```typescript
   // components/interactions/LikeAnimation.tsx
   - Heart fill animation
   - Particle effects
   - Count increment animation
   - Double-tap to like gesture
   ```

3. **Like Service**
   ```typescript
   // services/interaction-service.ts
   class InteractionService {
     async likeVideo(videoId: string): Promise<void>
     async unlikeVideo(videoId: string): Promise<void>
     async getLikeStatus(videoId: string): Promise<boolean>
   }
   ```

4. **Like Store (Zustand)**
   ```typescript
   // stores/interaction-store.ts
   interface InteractionState {
     likes: Map<string, boolean>;
     likeCounts: Map<string, number>;
     toggleLike: (videoId: string) => Promise<void>;
   }
   ```

### Phase 2: Mobile Like Implementation
1. **Like Widget (Flutter)**
   ```dart
   // features/interactions/widgets/like_button.dart
   class LikeButton extends StatefulWidget {
     final String videoId;
     final int likeCount;
     final bool isLiked;
     final Function(String) onLike;
   }
   ```

2. **Like Animation Controller**
   ```dart
   // features/interactions/widgets/like_animation.dart
   class LikeAnimation extends StatefulWidget {
     final bool isLiked;
     final VoidCallback onAnimationComplete;
   }
   ```

3. **Interaction BLoC**
   ```dart
   // features/interactions/bloc/interaction_bloc.dart
   class InteractionBloc extends Bloc<InteractionEvent, InteractionState> {
     on<VideoLiked>(_onVideoLiked);
     on<VideoUnliked>(_onVideoUnliked);
     on<CommentAdded>(_onCommentAdded);
   }
   ```

### Phase 3: Comment System (Web)
1. **Comment Section Component**
   ```typescript
   // components/comments/CommentSection.tsx
   interface CommentSectionProps {
     videoId: string;
     isOpen: boolean;
     onClose: () => void;
   }
   ```

2. **Comment Item Component**
   ```typescript
   // components/comments/CommentItem.tsx
   interface Comment {
     id: string;
     userId: string;
     username: string;
     profilePicture: string;
     content: string;
     createdAt: Date;
     likeCount: number;
     isLiked: boolean;
     replies: Comment[];
     parentId?: string;
   }
   ```

3. **Comment Input Component**
   ```typescript
   // components/comments/CommentInput.tsx
   - Text input with mention support
   - Emoji picker integration
   - Character limit indicator
   - Submit button with loading state
   ```

4. **Comment Thread Component**
   ```typescript
   // components/comments/CommentThread.tsx
   - Nested comment display
   - Reply functionality
   - Load more replies
   - Collapse/expand threads
   ```

### Phase 4: Mobile Comment System
1. **Comment Bottom Sheet**
   ```dart
   // features/comments/widgets/comment_bottom_sheet.dart
   class CommentBottomSheet extends StatefulWidget {
     final String videoId;
     final List<Comment> comments;
   }
   ```

2. **Comment List Widget**
   ```dart
   // features/comments/widgets/comment_list.dart
   class CommentList extends StatelessWidget {
     final List<Comment> comments;
     final Function(String) onReply;
   }
   ```

3. **Comment Input Widget**
   ```dart
   // features/comments/widgets/comment_input.dart
   class CommentInput extends StatefulWidget {
     final String videoId;
     final String? replyToCommentId;
   }
   ```

### Phase 5: Real-time Features
1. **WebSocket Integration (Web)**
   ```typescript
   // services/websocket-service.ts
   class WebSocketService {
     connect(videoId: string): void
     onNewComment(callback: (comment: Comment) => void): void
     onLikeUpdate(callback: (videoId: string, count: number) => void): void
   }
   ```

2. **Real-time Comment Updates**
   ```typescript
   // hooks/useRealTimeComments.ts
   const useRealTimeComments = (videoId: string) => {
     // WebSocket connection for live comments
     // Automatic comment list updates
     // Typing indicators
   }
   ```

3. **Push Notifications (Mobile)**
   ```dart
   // core/services/notification_service.dart
   class NotificationService {
     Future<void> showLikeNotification(String username);
     Future<void> showCommentNotification(String username, String comment);
   }
   ```

### Phase 6: Advanced Interaction Features
1. **Mention System**
   ```typescript
   // components/comments/MentionInput.tsx
   - User search and autocomplete
   - Mention highlighting
   - Notification triggers
   ```

2. **Emoji Reactions**
   ```typescript
   // components/interactions/EmojiReactions.tsx
   - Quick emoji responses
   - Reaction counts
   - Animated emoji display
   ```

3. **Comment Moderation**
   ```typescript
   // components/comments/CommentModeration.tsx
   - Report comment functionality
   - Hide/delete comments
   - User blocking
   ```

### Phase 7: Optimistic Updates
1. **Optimistic Like Updates**
   ```typescript
   // utils/optimistic-updates.ts
   class OptimisticUpdates {
     async optimisticLike(videoId: string): Promise<void>
     async revertLike(videoId: string): Promise<void>
   }
   ```

2. **Optimistic Comment Updates**
   ```typescript
   // stores/comment-store.ts
   interface CommentState {
     comments: Map<string, Comment[]>;
     pendingComments: Comment[];
     addOptimisticComment: (comment: Comment) => void;
   }
   ```

3. **Error Recovery**
   ```typescript
   // utils/error-recovery.ts
   - Retry failed interactions
   - Queue offline interactions
   - Sync when online
   ```

## Deliverables
- [ ] Like/unlike functionality with animations
- [ ] Comment system with threaded replies
- [ ] Real-time comment updates
- [ ] Mention and emoji support
- [ ] Comment moderation features
- [ ] Optimistic UI updates
- [ ] Mobile gesture interactions
- [ ] Push notification system
- [ ] Offline interaction queuing
- [ ] Error handling and recovery

## Acceptance Criteria
- Like button responds immediately with animation
- Comments load and display in threaded format
- Real-time updates work without page refresh
- Mentions trigger notifications correctly
- Comment moderation tools function properly
- Optimistic updates provide smooth UX
- Mobile gestures feel natural and responsive
- Push notifications work reliably
- Offline interactions sync when online
- Error states are handled gracefully

## Dependencies
- Task 01: Project Setup & Configuration
- Task 02: Authentication System
- Task 04: Video Feed Implementation
- Backend API for interaction endpoints
- WebSocket/real-time service setup

## Estimated Effort
- Web like system: 6-8 hours
- Mobile like implementation: 4-6 hours
- Web comment system: 12-15 hours
- Mobile comment system: 10-12 hours
- Real-time features: 8-10 hours
- Advanced features: 8-10 hours
- Optimistic updates: 6-8 hours
- Testing and optimization: 6-8 hours
- **Total: 60-77 hours**

## Notes
- Implement proper rate limiting for interactions
- Consider implementing comment threading depth limits
- Add accessibility features for screen readers
- Implement proper spam detection for comments
- Consider implementing comment reactions beyond likes
- Add support for comment editing and deletion
- Implement proper content filtering for comments
- Test interaction performance under high load
